# Rendi.dev Video Generation

This folder contains the implementation for server-side video generation using Rendi.dev.

## Architecture

The implementation follows a three-stage process:

### Stage 1: Frame Capture (Client-Side)
- Captures frames from p5.js canvas at 25fps
- Uses JPEG format with 0.8 quality for optimal balance between quality and file size
- Each frame is captured using `canvas.toDataURL('image/jpeg', 0.8)`

### Stage 2: Frame Upload (Supabase)
- Converts base64 image data to Blob
- Uploads each frame to Supabase Storage bucket
- Generates public URLs for each uploaded frame
- Uses unique session ID for organizing files

### Stage 3: Video Assembly (Rendi.dev)
- Creates a text file listing all frame URLs in FFmpeg concat format
- Uploads the file list to Supabase Storage
- Calls Rendi.dev API with FFmpeg command to assemble video

## Files

- `RendiVideoGenerator.tsx` - Main component for Rendi.dev video generation
- `README.md` - This documentation file

## API Route

The implementation uses `/api/rendi/route.ts` to securely call the Rendi.dev API from the server side.

## Environment Variables

Required environment variables (already configured):
- `RENDI_API_KEY` - API key for Rendi.dev
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- `NEXT_PUBLIC_SUPABASE_BUCKET` - Supabase storage bucket name

## FFmpeg Command

The implementation uses this FFmpeg command for video assembly:

```bash
ffmpeg -f concat -safe 0 -protocol_whitelist file,http,https,tcp,tls -i [FILE_LIST_URL] -r 25 -c:v libx264 -pix_fmt yuv420p output.mp4
```

## Benefits

- Server-side processing avoids browser memory limitations
- Better performance for longer videos
- More reliable than browser-based FFmpeg
- Automatic cleanup of temporary files
