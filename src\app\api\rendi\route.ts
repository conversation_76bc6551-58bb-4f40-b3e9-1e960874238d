import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { command } = await request.json();

    if (!command) {
      return NextResponse.json(
        { error: 'FFmpeg command is required' },
        { status: 400 }
      );
    }

    const apiKey = process.env.RENDI_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Rendi API key not configured' },
        { status: 500 }
      );
    }

    // Call Rendi.dev API
    const response = await fetch('https://api.rendi.dev/v1/render', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: command,
        output_format: 'mp4'
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Rendi API error:', errorText);
      return NextResponse.json(
        { error: `Rendi API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const result = await response.json();
    
    // Return the video URL from Rendi.dev
    return NextResponse.json({
      videoUrl: result.output_url || result.url,
      renderId: result.id,
      status: result.status
    });

  } catch (error) {
    console.error('Rendi API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
