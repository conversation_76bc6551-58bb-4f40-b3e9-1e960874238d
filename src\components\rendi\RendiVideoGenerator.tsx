'use client';

import { useState, useRef } from 'react';
import { Play, Download, Cloud } from 'lucide-react';
import p5 from 'p5';
import { createClient } from '@supabase/supabase-js';
import { AudioData, WaveformStyle, VideoSettings, VisualizationParams } from '../../types';

type ProcessingStatus = 'idle' | 'capturing_frames' | 'uploading_frames' | 'creating_video' | 'complete' | 'error';

interface RendiVideoGeneratorProps {
  audioData: AudioData;
  style: WaveformStyle;
  settings: VideoSettings;
  visualizationParams: VisualizationParams;
  onGenerationStart: () => void;
  onGenerationComplete: () => void;
}

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function RendiVideoGenerator({
  audioData,
  style,
  settings,
  visualizationParams,
  onGenerationStart,
  onGenerationComplete
}: RendiVideoGeneratorProps) {
  const [status, setStatus] = useState<ProcessingStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Helper function to create p5 instance for frame capture
  const createP5Instance = (width: number, height: number): Promise<p5> => {
    return new Promise((resolve) => {
      const sketch = (p: p5) => {
        p.setup = () => {
          p.createCanvas(width, height);
          p.colorMode(p.HSB, 360, 100, 100, 100);
          resolve(p);
        };
      };
      new p5(sketch);
    });
  };

  // Draw waveform function (copied from existing VideoGenerator)
  const drawP5Waveform = (p: p5, audioDataArray: Float32Array, style: WaveformStyle, time: number, width: number, height: number) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const scaleFactor = Math.min(width, height) / 720;
    
    p.background(0, 0, 5);
    
    const numBars = 128;
    const usedBins = Math.min(96, audioDataArray.length);
    const radius = Math.min(width, height) * 0.25 * scaleFactor;
    const maxBarLength = Math.min(width, height) * 0.2 * scaleFactor;
    
    for (let i = 0; i < numBars; i++) {
      const dataIndex = Math.floor((i / numBars) * usedBins);
      const amplitude = audioDataArray[dataIndex] || 0;
      const adjustedAmplitude = Math.pow(amplitude * visualizationParams.sensitivity, 0.7);
      const barLength = adjustedAmplitude * maxBarLength;
      
      const angle = (i / numBars) * p.TWO_PI - p.PI / 2;
      const x1 = centerX + p.cos(angle) * radius;
      const y1 = centerY + p.sin(angle) * radius;
      const x2 = centerX + p.cos(angle) * (radius + barLength);
      const y2 = centerY + p.sin(angle) * (radius + barLength);
      
      const hue = (i * 2.8 + time * 30) % 360;
      const saturation = 70 + adjustedAmplitude * 30;
      const brightness = 60 + adjustedAmplitude * 40;
      const alpha = 70 + adjustedAmplitude * 30;
      
      p.stroke(hue, saturation, brightness, alpha);
      p.strokeWeight(Math.max(1, Math.floor(3 * scaleFactor)));
      p.line(x1, y1, x2, y2);
    }
    
    const centerSize = Math.floor(20 * scaleFactor);
    p.fill(280, 80, 90, 80);
    p.noStroke();
    p.circle(centerX, centerY, centerSize);
    
    const pulseAmplitude = Math.floor(10 * scaleFactor);
    const pulseRadius = centerSize + p.sin(time * 4) * pulseAmplitude;
    p.noFill();
    p.stroke(280, 80, 90, 60);
    p.strokeWeight(Math.max(1, Math.floor(2 * scaleFactor)));
    p.circle(centerX, centerY, pulseRadius);
  };

  // Convert base64 to blob
  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };

  // Upload frame to Supabase
  const uploadFrame = async (frameBlob: Blob, frameNumber: number, sessionId: string): Promise<string> => {
    const fileName = `frame-${frameNumber.toString().padStart(6, '0')}.jpeg`;
    const filePath = `${sessionId}/${fileName}`;
    
    const { data, error } = await supabase.storage
      .from(process.env.NEXT_PUBLIC_SUPABASE_BUCKET!)
      .upload(filePath, frameBlob, {
        contentType: 'image/jpeg',
        cacheControl: '3600'
      });
    
    if (error) throw error;
    
    const { data: { publicUrl } } = supabase.storage
      .from(process.env.NEXT_PUBLIC_SUPABASE_BUCKET!)
      .getPublicUrl(filePath);
    
    return publicUrl;
  };

  // Create file list for FFmpeg
  const createFileList = (frameUrls: string[]): string => {
    return frameUrls.map(url => `file '${url}'`).join('\n');
  };

  // Upload file list to Supabase
  const uploadFileList = async (fileListContent: string, sessionId: string): Promise<string> => {
    const fileListBlob = new Blob([fileListContent], { type: 'text/plain' });
    const filePath = `${sessionId}/filelist.txt`;
    
    const { data, error } = await supabase.storage
      .from(process.env.NEXT_PUBLIC_SUPABASE_BUCKET!)
      .upload(filePath, fileListBlob, {
        contentType: 'text/plain',
        cacheControl: '3600'
      });
    
    if (error) throw error;
    
    const { data: { publicUrl } } = supabase.storage
      .from(process.env.NEXT_PUBLIC_SUPABASE_BUCKET!)
      .getPublicUrl(filePath);
    
    return publicUrl;
  };

  // Call Rendi.dev API
  const callRendiAPI = async (fileListUrl: string): Promise<string> => {
    const ffmpegCommand = [
      'ffmpeg',
      '-f', 'concat',
      '-safe', '0',
      '-protocol_whitelist', 'file,http,https,tcp,tls',
      '-i', fileListUrl,
      '-r', '25',
      '-c:v', 'libx264',
      '-pix_fmt', 'yuv420p',
      'output.mp4'
    ].join(' ');

    const response = await fetch('/api/rendi', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        command: ffmpegCommand
      })
    });

    if (!response.ok) {
      throw new Error(`Rendi API error: ${response.statusText}`);
    }

    const result = await response.json();
    return result.videoUrl;
  };

  const generateVideo = async () => {
    if (isGenerating) return;

    if (audioData.metadata.duration > 900) {
      setStatus('error');
      setMessage('Audio too long. Maximum duration is 15 minutes for video generation.');
      return;
    }

    setIsGenerating(true);
    onGenerationStart();
    setStatus('capturing_frames');
    setProgress(0);
    setMessage('Starting video generation with Rendi.dev...');

    let p5Instance: p5 | null = null;
    const sessionId = `video-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    try {
      const [width, height] = settings.resolution.split('x').map(Number);
      const totalFrames = Math.floor(audioData.metadata.duration * 25); // 25fps
      
      setMessage('Creating canvas and preparing frames...');
      p5Instance = await createP5Instance(width, height);

      // Stage 1: Capture frames
      setMessage('Capturing frames from canvas...');
      const frameUrls: string[] = [];
      
      for (let frame = 0; frame < totalFrames; frame++) {
        const currentTime = (frame / 25) / audioData.metadata.duration;
        const audioDataIndex = Math.floor(currentTime * audioData.frequencyData.length);
        const audioDataArray = audioData.frequencyData[audioDataIndex] || new Float32Array(128);
        
        const animationTime = currentTime * 2.5;
        drawP5Waveform(p5Instance, audioDataArray, style, animationTime, width, height);
        
        // Capture frame as JPEG
        const frameDataUrl = (p5Instance.drawingContext.canvas as HTMLCanvasElement).toDataURL('image/jpeg', 0.8);
        const frameBlob = base64ToBlob(frameDataUrl, 'image/jpeg');
        
        // Stage 2: Upload frame to Supabase
        const frameUrl = await uploadFrame(frameBlob, frame + 1, sessionId);
        frameUrls.push(frameUrl);
        
        const captureProgress = (frame / totalFrames) * 60;
        setProgress(captureProgress);
        setMessage(`Captured and uploaded frame ${frame + 1} of ${totalFrames}...`);
        
        if (frame % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      // Stage 3: Create file list and upload
      setStatus('uploading_frames');
      setProgress(70);
      setMessage('Creating file list for video assembly...');
      
      const fileListContent = createFileList(frameUrls);
      const fileListUrl = await uploadFileList(fileListContent, sessionId);
      
      // Stage 4: Call Rendi.dev API
      setStatus('creating_video');
      setProgress(80);
      setMessage('Sending to Rendi.dev for video creation...');
      
      const videoUrl = await callRendiAPI(fileListUrl);
      
      setVideoUrl(videoUrl);
      setProgress(100);
      setStatus('complete');
      setMessage('Video generated successfully with Rendi.dev!');

    } catch (error) {
      console.error('Rendi video generation error:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Failed to generate video with Rendi.dev');
    } finally {
      if (p5Instance) {
        p5Instance.remove();
      }
      setIsGenerating(false);
      onGenerationComplete();
    }
  };

  const downloadVideo = () => {
    if (videoUrl) {
      const a = document.createElement('a');
      a.href = videoUrl;
      a.download = `${audioData.metadata.fileName.replace(/\.[^/.]+$/, '')}-rendi-visualization.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'capturing_frames':
      case 'uploading_frames':
      case 'creating_video':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>;
      case 'complete':
        return <Download className="w-5 h-5" />;
      case 'error':
        return <span className="text-red-400">⚠</span>;
      default:
        return <Cloud className="w-5 h-5" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'complete':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-white';
    }
  };

  return (
    <div className="space-y-4">
      {/* Generation button */}
      <button
        onClick={generateVideo}
        disabled={isGenerating}
        className={`
          w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2
          ${isGenerating
            ? 'bg-gray-600 cursor-not-allowed'
            : 'bg-green-600 hover:bg-green-700 active:scale-95'
          }
        `}
      >
        {getStatusIcon()}
        <span className={getStatusColor()}>
          {isGenerating ? 'Generating with Rendi.dev...' : 'Generate Video (Rendi.dev)'}
        </span>
      </button>

      {/* Progress and status */}
      {(isGenerating || status === 'complete' || status === 'error') && (
        <div className="space-y-3">
          {/* Progress bar */}
          {isGenerating && (
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          )}
          
          {/* Status message */}
          <p className={`text-sm text-center ${getStatusColor()}`}>
            {message}
          </p>
          
          {/* Download button */}
          {status === 'complete' && videoUrl && (
            <button
              onClick={downloadVideo}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Download Video</span>
            </button>
          )}
        </div>
      )}
    </div>
  );
}
