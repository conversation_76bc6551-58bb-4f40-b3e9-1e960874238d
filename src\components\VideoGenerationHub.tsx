'use client';

import { useState } from 'react';
import { Monitor, Cloud } from 'lucide-react';
import VideoGenerator from './VideoGenerator';
import RendiVideoGenerator from './rendi/RendiVideoGenerator';
import { AudioData, WaveformStyle, VideoSettings, VisualizationParams } from '../types';

type GenerationMethod = 'browser' | 'rendi';

interface VideoGenerationHubProps {
  audioData: AudioData;
  style: WaveformStyle;
  settings: VideoSettings;
  visualizationParams: VisualizationParams;
  onGenerationStart: () => void;
  onGenerationComplete: () => void;
}

export default function VideoGenerationHub({
  audioData,
  style,
  settings,
  visualizationParams,
  onGenerationStart,
  onGenerationComplete
}: VideoGenerationHubProps) {
  const [selectedMethod, setSelectedMethod] = useState<GenerationMethod>('browser');

  return (
    <div className="space-y-6">
      {/* Method Selection */}
      <div className="space-y-3">
        <h3 className="text-lg font-medium text-white">Choose Generation Method</h3>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => setSelectedMethod('browser')}
            className={`
              p-4 rounded-lg border-2 transition-all duration-200 flex flex-col items-center space-y-2
              ${selectedMethod === 'browser'
                ? 'border-purple-500 bg-purple-500/20 text-white'
                : 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
              }
            `}
          >
            <Monitor className="w-6 h-6" />
            <div className="text-center">
              <div className="font-medium">Browser Processing</div>
              <div className="text-xs opacity-75">Local FFmpeg</div>
            </div>
          </button>
          
          <button
            onClick={() => setSelectedMethod('rendi')}
            className={`
              p-4 rounded-lg border-2 transition-all duration-200 flex flex-col items-center space-y-2
              ${selectedMethod === 'rendi'
                ? 'border-green-500 bg-green-500/20 text-white'
                : 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
              }
            `}
          >
            <Cloud className="w-6 h-6" />
            <div className="text-center">
              <div className="font-medium">Cloud Processing</div>
              <div className="text-xs opacity-75">Rendi.dev</div>
            </div>
          </button>
        </div>
      </div>

      {/* Method Description */}
      <div className="bg-gray-800/30 rounded-lg p-4 border border-gray-700">
        {selectedMethod === 'browser' ? (
          <div className="space-y-2">
            <h4 className="font-medium text-white flex items-center space-x-2">
              <Monitor className="w-4 h-4" />
              <span>Browser Processing</span>
            </h4>
            <p className="text-sm text-gray-300">
              Processes video locally in your browser using WebAssembly FFmpeg. 
              Good for shorter videos and when you want to keep everything local.
            </p>
            <div className="text-xs text-gray-400">
              • Fully local processing<br/>
              • No server dependencies<br/>
              • May be slower for long videos
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <h4 className="font-medium text-white flex items-center space-x-2">
              <Cloud className="w-4 h-4" />
              <span>Cloud Processing</span>
            </h4>
            <p className="text-sm text-gray-300">
              Uploads frames to Supabase and processes video on Rendi.dev servers. 
              Faster and more reliable for longer videos.
            </p>
            <div className="text-xs text-gray-400">
              • Server-side processing<br/>
              • Better for long videos<br/>
              • Requires internet connection
            </div>
          </div>
        )}
      </div>

      {/* Video Generator Component */}
      <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
        {selectedMethod === 'browser' ? (
          <VideoGenerator
            audioData={audioData}
            style={style}
            settings={settings}
            visualizationParams={visualizationParams}
            onGenerationStart={onGenerationStart}
            onGenerationComplete={onGenerationComplete}
          />
        ) : (
          <RendiVideoGenerator
            audioData={audioData}
            style={style}
            settings={settings}
            visualizationParams={visualizationParams}
            onGenerationStart={onGenerationStart}
            onGenerationComplete={onGenerationComplete}
          />
        )}
      </div>
    </div>
  );
}
